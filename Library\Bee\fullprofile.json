{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 24816, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 24816, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 24816, "tid": 16, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 24816, "tid": 16, "ts": 1749465484266698, "dur": 956, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 24816, "tid": 16, "ts": 1749465484271409, "dur": 776, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 24816, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 24816, "tid": 1, "ts": 1749465483420403, "dur": 9651, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 24816, "tid": 1, "ts": 1749465483430058, "dur": 45385, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 24816, "tid": 1, "ts": 1749465483475451, "dur": 48835, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 24816, "tid": 16, "ts": 1749465484272189, "dur": 1073, "ph": "X", "name": "", "args": {}}, {"pid": 24816, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483418640, "dur": 11831, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483430473, "dur": 826054, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483431454, "dur": 2616, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483434075, "dur": 1202, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483435279, "dur": 12847, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483448135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483448138, "dur": 456, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483448596, "dur": 530, "ph": "X", "name": "ProcessMessages 19848", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450059, "dur": 138, "ph": "X", "name": "ReadAsync 19848", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450199, "dur": 10, "ph": "X", "name": "ProcessMessages 20502", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450211, "dur": 28, "ph": "X", "name": "ReadAsync 20502", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450241, "dur": 24, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450269, "dur": 17, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450288, "dur": 18, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450308, "dur": 19, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450330, "dur": 24, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450356, "dur": 29, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450388, "dur": 22, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450413, "dur": 204, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450621, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450624, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450668, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450671, "dur": 72, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450748, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450789, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450792, "dur": 45, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450839, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450842, "dur": 35, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450882, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483450885, "dur": 879, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483451773, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483451860, "dur": 5, "ph": "X", "name": "ProcessMessages 4682", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483451866, "dur": 43, "ph": "X", "name": "ReadAsync 4682", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483451911, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483451945, "dur": 46, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483451996, "dur": 2, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452000, "dur": 40, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452044, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452046, "dur": 37, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452085, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452087, "dur": 167, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452261, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452314, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452317, "dur": 27, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452345, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452347, "dur": 29, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452380, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452382, "dur": 44, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452427, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452430, "dur": 31, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452462, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452464, "dur": 85, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452556, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452601, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452603, "dur": 43, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452649, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452651, "dur": 31, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452685, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483452712, "dur": 673, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453389, "dur": 62, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453453, "dur": 5, "ph": "X", "name": "ProcessMessages 6999", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453459, "dur": 38, "ph": "X", "name": "ReadAsync 6999", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453500, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453520, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453522, "dur": 20, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453543, "dur": 18, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453564, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453584, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453606, "dur": 247, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453856, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453882, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453883, "dur": 22, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453907, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453909, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453930, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453951, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453970, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483453989, "dur": 24, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454016, "dur": 57, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454075, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454098, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454122, "dur": 20, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454145, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454166, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454184, "dur": 114, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454300, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454326, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454353, "dur": 19, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454374, "dur": 17, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454393, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454413, "dur": 14, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454430, "dur": 16, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454448, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454471, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454493, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454514, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454533, "dur": 216, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454752, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454774, "dur": 19, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454796, "dur": 17, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454815, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454838, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454858, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454879, "dur": 15, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483454897, "dur": 762, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455662, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455731, "dur": 5, "ph": "X", "name": "ProcessMessages 8394", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455738, "dur": 20, "ph": "X", "name": "ReadAsync 8394", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455760, "dur": 121, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455884, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455904, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455929, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455948, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455969, "dur": 17, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483455988, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456007, "dur": 20, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456029, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456051, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456070, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456090, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456109, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456131, "dur": 16, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456149, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456170, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456192, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456214, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456233, "dur": 18, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456254, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456270, "dur": 19, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456291, "dur": 17, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456310, "dur": 19, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456330, "dur": 32, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456365, "dur": 34, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456401, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456402, "dur": 25, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456430, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456452, "dur": 17, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456471, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456489, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456490, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456510, "dur": 14, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456527, "dur": 18, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456547, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456569, "dur": 17, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456589, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456614, "dur": 23, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456638, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456640, "dur": 16, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456658, "dur": 20, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456679, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456682, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456710, "dur": 32, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456743, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456746, "dur": 27, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456776, "dur": 21, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456799, "dur": 121, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456923, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456958, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456961, "dur": 33, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456995, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483456998, "dur": 21, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457021, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457043, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457063, "dur": 18, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457085, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457823, "dur": 98, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457923, "dur": 7, "ph": "X", "name": "ProcessMessages 14576", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457932, "dur": 20, "ph": "X", "name": "ReadAsync 14576", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457954, "dur": 18, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457975, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483457994, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458012, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458033, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458059, "dur": 128, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458189, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458218, "dur": 25, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458245, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458264, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458283, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458306, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458327, "dur": 18, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458347, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458367, "dur": 17, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458385, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458387, "dur": 19, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458409, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458433, "dur": 17, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458452, "dur": 102, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458557, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458589, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458590, "dur": 27, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458619, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458640, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458642, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458662, "dur": 14, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458678, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458702, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458729, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458731, "dur": 35, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458771, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458774, "dur": 50, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458827, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458829, "dur": 31, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458865, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483458867, "dur": 664, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459534, "dur": 1, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459536, "dur": 40, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459577, "dur": 1, "ph": "X", "name": "ProcessMessages 2461", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459597, "dur": 57, "ph": "X", "name": "ReadAsync 2461", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459656, "dur": 4, "ph": "X", "name": "ProcessMessages 6239", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459661, "dur": 74, "ph": "X", "name": "ReadAsync 6239", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459737, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459764, "dur": 21, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459788, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459809, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459811, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459834, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459860, "dur": 118, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483459982, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460012, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460033, "dur": 18, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460054, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460073, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460094, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460113, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460114, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460133, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460156, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460176, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460194, "dur": 18, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460214, "dur": 18, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460234, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460255, "dur": 16, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460273, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460293, "dur": 16, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460312, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460333, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460358, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483460376, "dur": 812, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461194, "dur": 144, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461341, "dur": 10, "ph": "X", "name": "ProcessMessages 9928", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461352, "dur": 61, "ph": "X", "name": "ReadAsync 9928", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461415, "dur": 1, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461417, "dur": 14, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461434, "dur": 29, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461466, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461468, "dur": 38, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461509, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461511, "dur": 77, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461591, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461626, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461628, "dur": 25, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461655, "dur": 24, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461681, "dur": 28, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461712, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461714, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461747, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461749, "dur": 31, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461782, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461784, "dur": 56, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461842, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461845, "dur": 31, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461877, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461880, "dur": 36, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461919, "dur": 25, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461945, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461948, "dur": 27, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483461977, "dur": 31, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462013, "dur": 73, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462088, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462127, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462130, "dur": 58, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462191, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462194, "dur": 45, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462241, "dur": 2, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462244, "dur": 127, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462373, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462418, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462420, "dur": 35, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462458, "dur": 27, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462487, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462510, "dur": 25, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462539, "dur": 33, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462576, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462579, "dur": 38, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462620, "dur": 32, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462654, "dur": 3, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462659, "dur": 28, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462687, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462689, "dur": 26, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462718, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462757, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462759, "dur": 42, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462803, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462805, "dur": 39, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462846, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483462848, "dur": 2500, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465353, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465357, "dur": 108, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465469, "dur": 8, "ph": "X", "name": "ProcessMessages 6044", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465478, "dur": 339, "ph": "X", "name": "ReadAsync 6044", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465823, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465873, "dur": 3, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483465877, "dur": 368, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466248, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466284, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466287, "dur": 33, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466322, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466324, "dur": 345, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466673, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466725, "dur": 2, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466729, "dur": 35, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466767, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483466769, "dur": 279, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467052, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467092, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467094, "dur": 32, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467127, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467129, "dur": 404, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467536, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467573, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467575, "dur": 36, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467613, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467615, "dur": 18, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467637, "dur": 334, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483467974, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483468004, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483468005, "dur": 64, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483468073, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483468108, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483468110, "dur": 946, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469059, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469097, "dur": 1, "ph": "X", "name": "ProcessMessages 2451", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469100, "dur": 373, "ph": "X", "name": "ReadAsync 2451", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469476, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469511, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469513, "dur": 261, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469780, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469814, "dur": 142, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483469959, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470014, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470016, "dur": 30, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470049, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470050, "dur": 373, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470426, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470463, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470466, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470499, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470502, "dur": 312, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470817, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470852, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470856, "dur": 81, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470941, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470976, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483470978, "dur": 332, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471317, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471357, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471359, "dur": 46, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471409, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471447, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471449, "dur": 263, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471717, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471753, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471755, "dur": 58, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471817, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471853, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483471855, "dur": 396, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472255, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472291, "dur": 30, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472323, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472347, "dur": 19, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472368, "dur": 9, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472379, "dur": 364, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483472746, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473624, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473627, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473662, "dur": 2, "ph": "X", "name": "ProcessMessages 3393", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473665, "dur": 94, "ph": "X", "name": "ReadAsync 3393", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473763, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473807, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473809, "dur": 36, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473847, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473849, "dur": 39, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473890, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473892, "dur": 36, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473930, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473932, "dur": 31, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473966, "dur": 32, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483473999, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474001, "dur": 333, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474541, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474584, "dur": 2, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474588, "dur": 184, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474774, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474806, "dur": 102, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474910, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474945, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474947, "dur": 37, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474986, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483474988, "dur": 26, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475015, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475017, "dur": 19, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475038, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475064, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475066, "dur": 96, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475164, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475195, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475196, "dur": 247, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475446, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475482, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475484, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475511, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475513, "dur": 311, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475825, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475849, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475869, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483475892, "dur": 375, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476270, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476292, "dur": 14, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476309, "dur": 19, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476329, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476350, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476371, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476389, "dur": 19, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476411, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476429, "dur": 19, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476450, "dur": 373, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476827, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476870, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483476872, "dur": 189, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477063, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477093, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477125, "dur": 219, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477348, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477381, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477383, "dur": 24, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477409, "dur": 269, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477680, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477707, "dur": 23, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477732, "dur": 53, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477787, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477812, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483477833, "dur": 266, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478102, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478131, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478154, "dur": 14, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478169, "dur": 333, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478505, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478533, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478560, "dur": 23, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478586, "dur": 378, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478969, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483478994, "dur": 16, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479012, "dur": 18, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479032, "dur": 16, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479052, "dur": 17, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479070, "dur": 367, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479440, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479465, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479484, "dur": 16, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479502, "dur": 368, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479873, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479899, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479920, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483479939, "dur": 290, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480231, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480263, "dur": 25, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480291, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480313, "dur": 264, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480582, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480611, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480613, "dur": 31, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480647, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480651, "dur": 26, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483480680, "dur": 325, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481009, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481054, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481057, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481093, "dur": 388, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481484, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481520, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481522, "dur": 25, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481550, "dur": 409, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481963, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483481991, "dur": 47, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482042, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482045, "dur": 100, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482151, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482200, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482202, "dur": 32, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482235, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483482237, "dur": 1033, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483276, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483312, "dur": 1, "ph": "X", "name": "ProcessMessages 2480", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483314, "dur": 197, "ph": "X", "name": "ReadAsync 2480", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483515, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483536, "dur": 95, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483632, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483659, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483680, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483483682, "dur": 348, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484033, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484069, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484072, "dur": 92, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484167, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484209, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484211, "dur": 24, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484239, "dur": 237, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484479, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484525, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484526, "dur": 77, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484607, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484646, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484649, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484670, "dur": 295, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484967, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483484995, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485019, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485021, "dur": 107, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485131, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485164, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485166, "dur": 24, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485192, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485193, "dur": 190, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485386, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485413, "dur": 20, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485436, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485455, "dur": 318, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485777, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485809, "dur": 16, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485827, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485862, "dur": 26, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483485890, "dur": 341, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486234, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486259, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486282, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486302, "dur": 320, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486624, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486646, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486672, "dur": 19, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483486693, "dur": 331, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487028, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487051, "dur": 21, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487074, "dur": 24, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487101, "dur": 353, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487456, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487476, "dur": 23, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487503, "dur": 85, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487590, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483487613, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490165, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490169, "dur": 123, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490294, "dur": 9, "ph": "X", "name": "ProcessMessages 7220", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490304, "dur": 36, "ph": "X", "name": "ReadAsync 7220", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490342, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490344, "dur": 156, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490504, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490546, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490548, "dur": 223, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490774, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490813, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490815, "dur": 37, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490853, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490855, "dur": 34, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490891, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490893, "dur": 34, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490928, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490930, "dur": 29, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490963, "dur": 31, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490995, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483490997, "dur": 300, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491302, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491361, "dur": 2, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491364, "dur": 30, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491395, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491397, "dur": 26, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491425, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491427, "dur": 31, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491460, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491462, "dur": 108, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491573, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491601, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491603, "dur": 302, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491907, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491942, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483491944, "dur": 330, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483492278, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483492281, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483492349, "dur": 564, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483492917, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483492958, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483492961, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493007, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493011, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493048, "dur": 370, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493423, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493426, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493462, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493464, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493521, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493524, "dur": 289, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493815, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493816, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493841, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493844, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493893, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493921, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483493971, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494001, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494023, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494053, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494084, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494123, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494149, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494172, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494199, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494223, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494226, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494262, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494292, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494316, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494403, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494450, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494453, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494493, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494527, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494555, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483494580, "dur": 791, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495377, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495412, "dur": 4, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495417, "dur": 84, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495505, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495530, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495546, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495571, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495606, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495631, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495666, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495691, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495710, "dur": 9, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495720, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495748, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495773, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495807, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495840, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495879, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495909, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495934, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495965, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483495990, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496012, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496268, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496298, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496301, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496329, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496356, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496391, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496393, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496426, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496428, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496460, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496483, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496533, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496558, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496560, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496595, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496622, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496666, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496668, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496701, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496704, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496740, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496743, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496773, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496775, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496827, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496852, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496879, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496908, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496962, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483496993, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497027, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497056, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497103, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497105, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497143, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497145, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497186, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497189, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497222, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497225, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497262, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497265, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497296, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497298, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497337, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497340, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497371, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497373, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497410, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497412, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497444, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497446, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497479, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497505, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497529, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497560, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497593, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497595, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497632, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497634, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497660, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497662, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497705, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483497751, "dur": 857, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498611, "dur": 5, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498617, "dur": 34, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498655, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498658, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498685, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498686, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498709, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498736, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498756, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498758, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498792, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498851, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498881, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498882, "dur": 30, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498915, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498916, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498946, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498947, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498972, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483498994, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499028, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499067, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499105, "dur": 226, "ph": "X", "name": "ProcessMessages 11", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499333, "dur": 47, "ph": "X", "name": "ReadAsync 11", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499383, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499386, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499429, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483499457, "dur": 2557, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483502019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483502025, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483502072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483502074, "dur": 4337, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483506420, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483506424, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483506462, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483506464, "dur": 9081, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483515555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483515559, "dur": 1197, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483516761, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483516765, "dur": 236, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517004, "dur": 4, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517010, "dur": 48, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517061, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517064, "dur": 494, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517562, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517602, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517606, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517636, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517694, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517725, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517727, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517772, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517800, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517831, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517858, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517890, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517918, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517947, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483517985, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518013, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518039, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518076, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518108, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518152, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518183, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518210, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518279, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518303, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518304, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518331, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518351, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518431, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518468, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518500, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518528, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518591, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518618, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518655, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518680, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518706, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518730, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518771, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518789, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518823, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518954, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483518985, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519014, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519040, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519064, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519088, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519348, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519382, "dur": 526, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519910, "dur": 28, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519940, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519943, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519973, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483519996, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520008, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520055, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520089, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520127, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520184, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520197, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520236, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520256, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520281, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520284, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520302, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520328, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520428, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520458, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520550, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520575, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520648, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520671, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520799, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520826, "dur": 106, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520935, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483520972, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521021, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521042, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521113, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521139, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521200, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521223, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521243, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521325, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521345, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521477, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521494, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521522, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521548, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521567, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521631, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521648, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521658, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521878, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521913, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521936, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483521937, "dur": 318, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483522258, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483522283, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483522304, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483522393, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483522422, "dur": 550, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483522977, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523000, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523095, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523116, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523142, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523397, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523430, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523495, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523533, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523695, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523745, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523747, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523791, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483523999, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483524022, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483524270, "dur": 635, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483524908, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465483524910, "dur": 708725, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484233643, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484233646, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484233700, "dur": 1247, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484234950, "dur": 4050, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239009, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239076, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239080, "dur": 41, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239126, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239130, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484239169, "dur": 2144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484241318, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484241351, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484241380, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484241587, "dur": 4995, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484246585, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484246587, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484246983, "dur": 4, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484246989, "dur": 32, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247022, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247026, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247054, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247082, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247101, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247104, "dur": 116, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247223, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247224, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247258, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247263, "dur": 29, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247294, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247297, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247322, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247324, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247373, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247376, "dur": 27, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247404, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247406, "dur": 23, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247432, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247435, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247466, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247469, "dur": 43, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247514, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247517, "dur": 19, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247538, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247598, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247626, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247652, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247675, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247695, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484247725, "dur": 718, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484248447, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484248470, "dur": 243, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 24816, "tid": 12884901888, "ts": 1749465484248715, "dur": 6987, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 24816, "tid": 16, "ts": 1749465484273264, "dur": 1083, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 24816, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 24816, "tid": 8589934592, "ts": 1749465483416090, "dur": 108211, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 24816, "tid": 8589934592, "ts": 1749465483524303, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 24816, "tid": 8589934592, "ts": 1749465483524308, "dur": 1057, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 24816, "tid": 16, "ts": 1749465484274348, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 24816, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 24816, "tid": 4294967296, "ts": 1749465483271990, "dur": 985405, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 24816, "tid": 4294967296, "ts": 1749465483278924, "dur": 130676, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 24816, "tid": 4294967296, "ts": 1749465484257573, "dur": 6298, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 24816, "tid": 4294967296, "ts": 1749465484260371, "dur": 2193, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 24816, "tid": 4294967296, "ts": 1749465484263945, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 24816, "tid": 16, "ts": 1749465484274354, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749465483423482, "dur": 3065, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465483426557, "dur": 20011, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465483446705, "dur": 73, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749465483446779, "dur": 431, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465483448451, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749465483450229, "dur": 258, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_C62A822DD3008F7A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749465483452070, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_407F07063643C512.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749465483455961, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1749465483458126, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_0F50152946DB09D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1749465483461431, "dur": 193, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483463740, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483464348, "dur": 218, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483468274, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483469779, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483471117, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483471603, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483472011, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483475077, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483477130, "dur": 224, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483477994, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483482292, "dur": 147, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483483818, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483484337, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483484781, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483485289, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483487770, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483489945, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483490492, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1749465483490589, "dur": 197, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465483447231, "dur": 44968, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465483492214, "dur": 755803, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465484248018, "dur": 323, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465484248388, "dur": 144, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749465484248725, "dur": 1021, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749465483447466, "dur": 44761, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483492244, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749465483492567, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483493196, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749465483493654, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483493844, "dur": 618, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483494521, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483494708, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483495081, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483495592, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749465483495877, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483496441, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1749465483496691, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483496983, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483497438, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483497688, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483497806, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483498049, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483498453, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2015906760990926514.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483498618, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483498930, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483499146, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749465483499412, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483499750, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483500161, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483500603, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483501695, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483502154, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483502911, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483503762, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483504414, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483505312, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483505736, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483506044, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483506423, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483507010, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483507360, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483507972, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483508506, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483508910, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483509455, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483510242, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483510986, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483511754, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483512309, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483512714, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483513164, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483513985, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@c132dced4a5c\\UnityEditor.TestRunner\\UnityTestProtocol\\Messages\\ScreenSettingsMessage.cs"}}, {"pid": 12345, "tid": 1, "ts": 1749465483513891, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483514775, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483514968, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483515602, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749465483515801, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749465483515983, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483516080, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749465483516587, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483516672, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749465483516896, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749465483517410, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483517630, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483517707, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749465483517895, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483518132, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749465483518526, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483518612, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483518797, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465483519151, "dur": 716699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484235852, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749465484238551, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484239296, "dur": 3605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749465484242902, "dur": 1045, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484243951, "dur": 2461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749465484246412, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484247054, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484247243, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484247371, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484247493, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749465484247546, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749465484247800, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483447562, "dur": 44691, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483492257, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_32C818CD7980EFF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749465483492581, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483493173, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749465483493365, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483493842, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483494555, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483494847, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483495202, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483495788, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483496385, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483496635, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483496807, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1749465483497055, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483497476, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483497919, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483498145, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483498365, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483498463, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483498675, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483499051, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1749465483499260, "dur": 1649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483500910, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483501332, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483501740, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483502095, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483502800, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483503668, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483504359, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483505105, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483505359, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483505747, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483506161, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483506569, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483507077, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483508000, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483508682, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483509110, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483510514, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483511393, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483512321, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483513141, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483513964, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483514613, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483514974, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483515546, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749465483515757, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749465483516433, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483516652, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749465483516833, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749465483517254, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483517498, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749465483517920, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483517981, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483518290, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483518767, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483519119, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749465483519242, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483519324, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749465483519605, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465483519726, "dur": 716104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465484235832, "dur": 2828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749465484238661, "dur": 627, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465484239317, "dur": 2998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749465484242316, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465484242380, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749465484244794, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749465484247249, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465484247497, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749465484247786, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483447504, "dur": 44740, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483492249, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1749465483492480, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483492550, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_92D06276DA3B75A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749465483492778, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483493198, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483493713, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483494138, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483494335, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483494582, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_2272D339B0EB3A67.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749465483494679, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483494782, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483495114, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483495646, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749465483496019, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749465483496525, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1749465483496714, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483497512, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483497698, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483497983, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483498217, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483498379, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483498681, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483498856, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483499119, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1749465483499435, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483499904, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483500342, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483500904, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483501202, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483501789, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483502345, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483502711, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483503506, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483504412, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483505190, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483505611, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483505979, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483506342, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483506788, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483507291, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483507793, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483508188, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483508828, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483509328, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483510360, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483510973, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483512261, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483513034, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483513752, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483514609, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483514983, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483515573, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749465483515788, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749465483516382, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483516579, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749465483517073, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483517201, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483517625, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483517834, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483518298, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483518800, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483519129, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483520280, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749465483520455, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749465483520775, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483520843, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465483520936, "dur": 714891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465484235830, "dur": 2864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749465484238695, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465484239396, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749465484242300, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465484242359, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749465484244867, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749465484247234, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749465484247636, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749465484247774, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483447623, "dur": 44641, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483492270, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_1DD292255763438B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749465483492541, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483493134, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483493821, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483494185, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483494375, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483494586, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483494693, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483494834, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483495266, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749465483495561, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749465483495885, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749465483496153, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749465483496355, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483496555, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483496844, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749465483497127, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483497509, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483497706, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483498123, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483498271, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483498447, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483498654, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483498960, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749465483499283, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483499791, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483500326, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483500782, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@624f919d8cdd\\Editor\\Editors\\CinemachineSplineDollyEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749465483500782, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483502171, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Utilities\\SDF\\BakeTool\\SdfBakerPreview.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749465483501982, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483503401, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483504187, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483505377, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483506126, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483506564, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483507019, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483507517, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483508423, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483508821, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483509671, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483510502, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483511069, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483511555, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483512090, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483512891, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483513762, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483514481, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483514964, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483515593, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749465483515798, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749465483516022, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749465483516541, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483516769, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749465483516939, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483517173, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749465483517697, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483517998, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749465483518182, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749465483518650, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483518776, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483519146, "dur": 8380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465483527527, "dur": 708279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465484235808, "dur": 2442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749465484238251, "dur": 1621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465484239881, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749465484242595, "dur": 2215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749465484244811, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465484245195, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749465484247696, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749465484247797, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483447727, "dur": 44563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483492294, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_9C4CF4B6FE076BF3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483492379, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483493120, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483493724, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483494085, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483494332, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483494533, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483494710, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483495078, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749465483495784, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483495929, "dur": 8011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483503942, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483504087, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483504227, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483504337, "dur": 10518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483514856, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483515021, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483515133, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483515544, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483515729, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483516498, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483516645, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483516765, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483517199, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483517816, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483518135, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483518300, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483518482, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483519115, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483519280, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483519670, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483519769, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483520275, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483520377, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483521411, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483521509, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483521612, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483522565, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483522670, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483523169, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465483523277, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483523380, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483523800, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749465483523951, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483524279, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749465483524542, "dur": 711311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465484235858, "dur": 2822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749465484238681, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465484239348, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749465484241649, "dur": 2215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465484243871, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749465484246402, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465484246564, "dur": 853, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749465484247765, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483447665, "dur": 44614, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483492286, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_9112B3CA45DA26CE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749465483492570, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483493098, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483493710, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_05E0427E0A0FE80D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749465483493874, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483494196, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483494540, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483494748, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483495152, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749465483495354, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483495605, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483496023, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483496313, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483497574, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1749465483497840, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483498063, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483498162, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483498361, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483498521, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483498847, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483499053, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749465483499253, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483500420, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483500950, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483501579, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@624f919d8cdd\\Runtime\\Behaviours\\CinemachineExternalCamera.cs"}}, {"pid": 12345, "tid": 6, "ts": 1749465483501384, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483502611, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483503917, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483504526, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483504937, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483505401, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483506001, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483506503, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483507085, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483508196, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483508694, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483509064, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483509713, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483510466, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483511479, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483512010, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483512785, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483513160, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483513649, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483514652, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483514971, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483515558, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749465483515772, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749465483516319, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483516556, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483516677, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749465483516825, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483516912, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749465483517404, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483517535, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749465483517695, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483517875, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749465483518293, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483518378, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483518792, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483519142, "dur": 6536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483526783, "dur": 96, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1749465483526879, "dur": 605, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1749465483525679, "dur": 1841, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465483527520, "dur": 708289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465484235820, "dur": 2837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749465484238659, "dur": 798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465484239467, "dur": 2998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749465484242509, "dur": 2261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749465484244771, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465484244840, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749465484247228, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749465484247533, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1749465484247758, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483447755, "dur": 44571, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483492379, "dur": 893, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483493300, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483493956, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483494312, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_A11EFFC223F8866B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749465483494431, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483494908, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483495222, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483495819, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1749465483496036, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483496268, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749465483496433, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483496705, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483497506, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483497706, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483498110, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483498221, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483498359, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483498512, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483498746, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483499205, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749465483499394, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483499839, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483500691, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483501804, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483502333, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483503420, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483504150, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483504553, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483504982, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483505588, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483506436, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483507492, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483508355, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483508865, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483509590, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483510371, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483511424, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483512044, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483512735, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483513367, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483513898, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483514498, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483514963, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483515548, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749465483515761, "dur": 1416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749465483517178, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483517346, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749465483517499, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483517714, "dur": 1170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749465483518963, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749465483519058, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749465483520210, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483520336, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749465483520605, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749465483520990, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465483521101, "dur": 714756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465484235859, "dur": 2758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749465484238618, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465484239305, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749465484241591, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465484242232, "dur": 2280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749465484244513, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465484244818, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749465484247256, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749465484247755, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483447814, "dur": 44529, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483492351, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_F448627E0B7C5355.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749465483492596, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483493150, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483493689, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_99EDDAA1E2125F9F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749465483493833, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483494184, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483494419, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483494714, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483495071, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749465483495264, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483495534, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483496093, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483496333, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483496547, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483496638, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483497066, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483497698, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483497917, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483498059, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483498364, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749465483498700, "dur": 714, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483499417, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483499827, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483500620, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483501318, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483501918, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483502623, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483503426, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483504157, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483504913, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483505827, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@fe5368074162\\Runtime\\2D\\UTess2D\\UTess.cs"}}, {"pid": 12345, "tid": 8, "ts": 1749465483505827, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483506756, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483507263, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483507848, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483508254, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483509066, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483509546, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483510072, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483510546, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483511293, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483511735, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483512369, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483512843, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483513249, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483513630, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483515400, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483515590, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749465483515746, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483516064, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749465483516664, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483517116, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483517171, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483517351, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749465483517511, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483517653, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749465483518068, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483518461, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483518806, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483519125, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749465483519277, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749465483519636, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465483519727, "dur": 716132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465484235864, "dur": 2818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749465484238683, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465484239324, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749465484239383, "dur": 2906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749465484242289, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465484242700, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749465484244990, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465484245058, "dur": 2359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749465484247418, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749465484247774, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483447848, "dur": 44510, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483492366, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_9C6FA246188F95D9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483492593, "dur": 619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483493220, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_AB8AD933C13C8818.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483493351, "dur": 652, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483494017, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483494415, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483494703, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483494997, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483495254, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483495487, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749465483495585, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749465483495859, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749465483496110, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1749465483496388, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483496711, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483497020, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483497504, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483497898, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483498140, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483498372, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6154019870087291391.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483498600, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483499015, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483499123, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1749465483499349, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483499405, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483499773, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483500504, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483500927, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483502171, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@624f919d8cdd\\Runtime\\Behaviours\\CinemachineSplineCart.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749465483501342, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483502869, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483503757, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483504200, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483504967, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483505445, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483506132, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483506584, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483506995, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483507899, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483508286, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483508928, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483509926, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483510216, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483510971, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483511395, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483512021, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483513980, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\MoveDeleteMenu.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749465483513002, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483514524, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483514987, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483515584, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483515799, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483516447, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483516539, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483516674, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483517135, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483517238, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483517354, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483517506, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483517712, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483518115, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483518299, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483518766, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483519125, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483519264, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483519348, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483519651, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483519753, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483519969, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483520460, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483520584, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483520714, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483521012, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483521095, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749465483521308, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749465483521844, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465483521950, "dur": 713871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465484235855, "dur": 2817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749465484238673, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465484239268, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749465484241575, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465484241797, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749465484244204, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749465484244837, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749465484247617, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749465484247756, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483447879, "dur": 44507, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483492395, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_07EF82A58738E0E0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749465483492581, "dur": 599, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483493186, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749465483493336, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483493843, "dur": 634, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483494543, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483494779, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483495099, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483495225, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749465483495307, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483495529, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483495857, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749465483496072, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483496533, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749465483496768, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483497068, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483497311, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483497601, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1749465483497985, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483498107, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483498285, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483498468, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483498801, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483498993, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14502112590723880366.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749465483499262, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483500074, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483501046, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483501522, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483502063, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483502545, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483503435, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483503816, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483504380, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483505337, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483505758, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483506484, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483506994, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483507440, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483508081, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483508453, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483508816, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483509347, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483510049, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483510468, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483510957, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483511987, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483512782, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483513261, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483513979, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Runtime\\Intrinsics\\x86\\Sse4_1.cs"}}, {"pid": 12345, "tid": 10, "ts": 1749465483513621, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483514577, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483515004, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483515586, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749465483515804, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749465483517082, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483517178, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749465483517436, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749465483517627, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749465483518044, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483518163, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483518309, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483518765, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483519121, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749465483519288, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749465483519559, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465483519648, "dur": 716245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465484235894, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749465484238614, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465484239273, "dur": 3062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749465484242335, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465484242855, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749465484245126, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749465484247576, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465484247750, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749465484247905, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483447903, "dur": 44499, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483492408, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1A45BE15294AD227.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749465483492613, "dur": 597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483493255, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483493909, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483494390, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483494596, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_75FF79AAC25CE47F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749465483494683, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483494794, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483495076, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749465483495305, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749465483495445, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483495856, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483496031, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483496210, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1749465483496395, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483496564, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483496693, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483496959, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483497251, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483497706, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483498277, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483498542, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483498824, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483498986, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749465483499278, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483500061, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483500793, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\MultiplayerCenterWindow\\RecommendationTabView.cs"}}, {"pid": 12345, "tid": 11, "ts": 1749465483500565, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483501981, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483502818, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483503822, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483504517, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483505112, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483505382, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483506383, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483507303, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483508309, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483509444, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483510374, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483511052, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483511976, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483512587, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483513162, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483513904, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483514544, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483514972, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483515550, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749465483516137, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749465483516877, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483517041, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749465483517239, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749465483518217, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483518486, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483518551, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483518783, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483519137, "dur": 4147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465483523285, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749465483523405, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749465483523683, "dur": 712196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465484235888, "dur": 2738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749465484238627, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465484239370, "dur": 2927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749465484242297, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465484242355, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749465484244754, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749465484247254, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465484247435, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749465484247764, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483447927, "dur": 44496, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483492427, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BEF5CA1EC64E588B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483492607, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483493197, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F75569858C7FEC6C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483493347, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483493814, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_4BD0EBD744A8A882.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483493954, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483494341, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483494539, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483494744, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483495084, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0CD7A7097DA784AD.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483495303, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1749465483495533, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483495667, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749465483495932, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749465483496242, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1749465483496457, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483496984, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483497239, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483497882, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483498032, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483498212, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483498374, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483498509, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483498828, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483498982, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749465483499225, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483500002, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483500789, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\Recommendations\\RecommenderSystemData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749465483500433, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483501399, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483502337, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483503396, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483503860, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483504527, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483504964, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483505542, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483506264, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483506845, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483507423, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483508169, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483508968, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483509396, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483510191, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483511086, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Runtime\\SplineRange.cs"}}, {"pid": 12345, "tid": 12, "ts": 1749465483511086, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483512086, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483512560, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483512997, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483513619, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483514504, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483515001, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483515582, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483515784, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749465483516378, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483516609, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483516921, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483517102, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749465483517686, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483517905, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483518285, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749465483518434, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749465483518763, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1749465483519164, "dur": 146, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465483519654, "dur": 714213, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1749465484235804, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749465484238232, "dur": 1024, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484239279, "dur": 2280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749465484241564, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484241647, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749465484243915, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484243981, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749465484246449, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484246560, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484246713, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484246988, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484247251, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749465484247763, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483447977, "dur": 44474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483492460, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_7D33EB796E04AD1F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483492715, "dur": 552, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483493277, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_BB80B5CC0580A018.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483493651, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483493969, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_2DAFA3DFB36A4B25.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483494094, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483494588, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483494697, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483494883, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483495190, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483495376, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483496034, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483496373, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483496621, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483496781, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483497434, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483497696, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483497926, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483498252, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483498370, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483498670, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483499017, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483499219, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749465483499512, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483499968, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483500294, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483500736, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483501463, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483501898, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483502511, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483503859, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483504387, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483505014, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Drawing\\Views\\ContextView.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749465483504854, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483506037, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483506594, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483507028, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483507478, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483508042, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483508567, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483508949, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483509851, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483510620, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483511714, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483512312, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483512992, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483513646, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483515237, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483515589, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483515792, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749465483516393, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483516656, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483516833, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483516898, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749465483517412, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483517805, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483518287, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483518571, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749465483518985, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483519129, "dur": 1211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483520342, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483520514, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749465483521236, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483521333, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465483521398, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749465483521784, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749465483521913, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749465483522184, "dur": 713682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465484235872, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749465484238379, "dur": 917, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465484239297, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749465484239352, "dur": 2812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749465484242208, "dur": 2287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749465484244496, "dur": 1179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749465484245680, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749465484247898, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483447948, "dur": 44482, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483492470, "dur": 789, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483493286, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483493915, "dur": 507, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483494508, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_77C9B2F6CDEA9940.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749465483494732, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483495041, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_69A7891FFD914456.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749465483495194, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483495530, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483495937, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483496337, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483496569, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483496771, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483497135, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483497517, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483497795, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483498001, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483498466, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483498839, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483499049, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749465483499311, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483499707, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483500304, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483500789, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@624f919d8cdd\\Editor\\Editors\\CinemachineCameraManagerEventsEditor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749465483500789, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483501840, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483502272, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483502880, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483503752, "dur": 1762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483505514, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483506048, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483506420, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483506962, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483507420, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483507957, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483508384, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483508795, "dur": 1028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483509823, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483511087, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Events\\IEventMachine.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749465483510919, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483511950, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483512634, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483513083, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483513982, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@c132dced4a5c\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolListener.cs"}}, {"pid": 12345, "tid": 14, "ts": 1749465483513878, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483514632, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483514970, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483515577, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749465483515866, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749465483516402, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483516772, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749465483516948, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483517236, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749465483517787, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483518119, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483518206, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483518289, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749465483518557, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749465483518896, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483519141, "dur": 4663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483523806, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749465483523950, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465483524020, "dur": 711797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465484235820, "dur": 2760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749465484238581, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465484239321, "dur": 2953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749465484242274, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465484242488, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749465484244788, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749465484245458, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749465484247801, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483447998, "dur": 44468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483492472, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_217E1FA7BA4C2F92.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749465483492696, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483493295, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483494054, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483494287, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_100C028F850EE8CE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749465483494390, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483494556, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483494813, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483495123, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483495686, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749465483495787, "dur": 6334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749465483502123, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483502329, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483502823, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483504295, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483505521, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483506011, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483506862, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483507707, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483507973, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483508964, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483509394, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483510433, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483511168, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483511422, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483512032, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483513054, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483513469, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483514017, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483514502, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483514981, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483515559, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749465483515728, "dur": 1550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483517283, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749465483518160, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483518360, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483518764, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465483518967, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749465483519236, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749465483520229, "dur": 715584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484235816, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749465484238765, "dur": 525, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484239303, "dur": 3165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749465484242512, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749465484244736, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484244889, "dur": 2308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749465484247197, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484247304, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484247483, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484247590, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749465484247753, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483448026, "dur": 44455, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483492516, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483493179, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749465483493261, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483493836, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483494253, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_BE0B22B212C39490.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749465483494417, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483494703, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483495014, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483495343, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483495608, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749465483495905, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483496216, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1749465483496447, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483496892, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749465483497183, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483497298, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483497584, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1749465483497776, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483498112, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483498368, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483498620, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483498922, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483499145, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749465483499318, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483499475, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483500147, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483500612, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483501537, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483502055, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483502922, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483503914, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483504675, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483505496, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483506212, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483506931, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483508112, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483508940, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483509448, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483511012, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483511471, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483512036, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483512987, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483513532, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483513954, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@c132dced4a5c\\UnityEditor.TestRunner\\TestRun\\Tasks\\Scene\\SaveModifiedSceneTask.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749465483513954, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483514738, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483514976, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483515598, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749465483515777, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483515971, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749465483516630, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483516748, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483516891, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483517280, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483517348, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749465483518054, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749465483518485, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483518603, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483518800, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483519136, "dur": 2104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483521241, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749465483521407, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749465483521738, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465483521838, "dur": 713966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465484235815, "dur": 2435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749465484238250, "dur": 1235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465484239494, "dur": 2959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749465484242454, "dur": 731, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465484243190, "dur": 2416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749465484245607, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749465484245785, "dur": 2131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1749465484254077, "dur": 1577, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 24816, "tid": 16, "ts": 1749465484274777, "dur": 2029, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 24816, "tid": 16, "ts": 1749465484276833, "dur": 1872, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 24816, "tid": 16, "ts": 1749465484269911, "dur": 9736, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}