namespace UnityEditor.Timeline
{
    static class WindowConstants
    {
        public const float timeAreaYPosition = 19.0f;
        public const float timeAreaHeight = 22.0f;
        public const float timeAreaMinWidth = 50.0f;
        public const float timeAreaShownRangePadding = 5.0f;

        public const float markerRowHeight = 18.0f;
        public const float markerRowYPosition = timeAreaYPosition + timeAreaHeight;

        public const float defaultHeaderWidth = 315.0f;
        public const float defaultBindingAreaWidth = 40.0f;

        public const float minHeaderWidth = 195.0f;
        public const float maxHeaderWidth = 650.0f;
        public const float headerSplitterWidth = 6.0f;
        public const float headerSplitterVisualWidth = 2.0f;

        public const float maxTimeAreaScaling = 90000.0f;
        public const float timeCodeWidth = 100.0f; // Enough space to display up to 9999 without clipping

        public const float sliderWidth = 15;
        public const float shadowUnderTimelineHeight = 15.0f;
        public const float createButtonWidth = 70.0f;

        public const float selectorWidth = 23.0f;
        public const float cogButtonWidth = 25.0f;

        public const float trackHeaderBindingHeight = 18.0f;
        public const float trackHeaderButtonSize = 16.0f;
        public const float trackHeaderButtonPadding = 2.0f;
        public const float trackBindingMaxSize = 300.0f;
        public const float trackBindingPadding = 5.0f;

        public const float trackInsertionMarkerHeight = 1f;
        public const float trackResizeHandleHeight = 7f;
        public const float inlineCurveContentPadding = 2.0f;

        public const float playControlsWidth = 300;

        public const int autoPanPaddingInPixels = 50;

        public const float overlayTextPadding = 40.0f;
    }
}
